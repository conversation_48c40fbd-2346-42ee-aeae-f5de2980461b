import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '../i18n';

export type LanguageCode = 'en' | 'ar';
export type LanguageType = 'english' | 'arabic';

const LANGUAGE_STORAGE_KEY = 'user-language';

// Map between UI language types and i18n language codes
export const languageMap: Record<LanguageType, LanguageCode> = {
  english: 'en',
  arabic: 'ar',
};

export const reverseLanguageMap: Record<LanguageCode, LanguageType> = {
  en: 'english',
  ar: 'arabic',
};

/**
 * Save the selected language to AsyncStorage and update i18n
 */
export const saveLanguage = async (languageType: LanguageType): Promise<void> => {
  try {
    const languageCode = languageMap[languageType];

    // Try to save to AsyncStorage, but don't fail if it's not available
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
    } catch (storageError) {
      console.warn('AsyncStorage not available, language will not persist:', storageError);
    }

    // Always try to change the i18n language
    await i18n.changeLanguage(languageCode);
    console.log(`Language changed to: ${languageCode}`);
  } catch (error) {
    console.error('Error saving language:', error);
    throw error;
  }
};

/**
 * Get the saved language from AsyncStorage
 */
export const getSavedLanguage = async (): Promise<LanguageCode> => {
  try {
    const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    return (savedLanguage as LanguageCode) || 'en';
  } catch (error) {
    console.warn('Error getting saved language, using default:', error);
    return 'en';
  }
};

/**
 * Get the current language type for UI
 */
export const getCurrentLanguageType = (): LanguageType => {
  const currentLanguage = i18n.language as LanguageCode;
  return reverseLanguageMap[currentLanguage] || 'english';
};

/**
 * Check if current language is RTL
 */
export const isRTL = (): boolean => {
  return i18n.language === 'ar';
};
