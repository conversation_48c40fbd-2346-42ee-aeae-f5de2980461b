import OnboardingSlider from '@/components/OnboardingSlider';
import { useRouter } from 'expo-router';
import React from 'react';
import {
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const onboardingData = [
  {
    id: '1',
    image: require('@/assets/images/onboarding/1.png'),
    title: 'Ship Your Car with Ease',
    description: 'Whether you\'re relocating or selling your car, SAFQA Shipping connects you with trusted local transporters to move your vehicle safely and quickly.',
  },
  {
    id: '2',
    image: require('@/assets/images/onboarding/2.png'),
    title: 'From Pickup to Drop-Off',
    description: 'Track your car in real-time, get status updates, and choose from various truck types — all designed to fit your transport needs perfectly.',
  },
  {
    id: '3',
    image: require('@/assets/images/onboarding/3.png'),
    title: 'For Drivers. By Drivers.',
    description: 'Are you a transporter? Join our network, receive nearby shipping requests, bid your price, and grow your business with SAFQA.',
  },
];

export default function OnboardingScreen() {
  const router = useRouter();

  const handleSignIn = () => {
    // Navigate to sign in screen
    console.log('Navigate to sign in');
  };

  const handleJoinNow = () => {
    // Navigate to registration screen
    console.log('Navigate to registration');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Logo */}
      <View className="items-center pt-12 pb-8">
        <Image
          source={require('@/assets/images/logo.png')}
          className="w-52 h-20"
          resizeMode="contain"
        />
      </View>

      {/* Slider */}
      <View className="flex-1">
        <OnboardingSlider slides={onboardingData} />
      </View>

      {/* Buttons */}
      <View className="px-6 pb-6">
        {/* Sign In Button */}
        <TouchableOpacity
          onPress={handleSignIn}
          className="bg-primary rounded-xl py-4 mb-6"
          activeOpacity={0.8}
        >
          <Text className="text-center text-black text-lg font-cairo-semibold">
            Sign in
          </Text>
        </TouchableOpacity>

        {/* Join Now Button */}
        <TouchableOpacity
          onPress={handleJoinNow}
          className="p-3 w-full border border-[#E6E6E6] rounded-xl"
          activeOpacity={0.8}
        >
          <Text className="text-center text-black text-base font-cairo-regular">
            New to SAFQA Shipping? <Text className="font-cairo-semibold">Join Now</Text>
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
