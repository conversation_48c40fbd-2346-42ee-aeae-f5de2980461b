import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  ImageBackground,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

type Language = 'english' | 'arabic';

export default function ChooseLanguageScreen() {
  const router = useRouter();

  const [selectedLanguage, setSelectedLanguage] = useState<Language>('english');

  const handleLanguageSelect = (language: Language) => {
    setSelectedLanguage(language);
  };

  const handleContinue = () => {
    // Handle continue action
    console.log('Selected language:', selectedLanguage);

    // go to onboarding screen
    router.push('/onboarding');
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />

      {/* Header with Logo */}
      <View className="px-6 pt-8 pb-4">
        <Image
          source={require('../assets/images/logo.png')}
          className="w-40 h-20"
          resizeMode="contain"
        />
      </View>

      {/* Content */}
      <View className="flex-1 px-6">
        {/* Title and Description */}
        <View className="mb-8">
          <Text className="mb-3 text-2xl text-gray-900 font-cairo-bold">
            Choose Language
          </Text>
          <Text className="text-base leading-6 text-gray-600 font-cairo-regular">
            Your language preferenced can be changed anytime in settings
          </Text>
        </View>

        {/* Language Selection Cards */}
        <View className="flex-row gap-4 mb-8">
          {/* English Card */}
          <TouchableOpacity
            className="flex-1 h-48 overflow-hidden rounded-2xl"
            onPress={() => handleLanguageSelect('english')}
            activeOpacity={0.8}
          >
            <View className="relative flex-1 bg-blue-400">
              {/* Background Image */}
              <ImageBackground
                source={require('../assets/images/onboarding/english.png')}
                className="absolute bottom-0 left-0 right-0 h-20"
                resizeMode="cover"
              />

              {/* Content */}
              <View className="justify-between flex-1 p-4">
                <View className="flex-row justify-between">
                  {/* Language Text */}
                  <Text className="text-lg text-white font-cairo-semibold">
                    English
                  </Text>

                  {/* Radio Button */}
                  <View className="items-end">
                    <View className={`w-6 h-6 rounded-full border-2 border-white ${selectedLanguage === 'english' ? 'bg-white' : 'bg-transparent'
                      }`}>
                      {selectedLanguage === 'english' && (
                        <View className="w-2.5 h-2.5 bg-blue-400 rounded-full m-auto" />
                      )}
                    </View>
                  </View>
                </View>

                {/* Letter Image */}
                <View className="items-center mb-8">
                  <Image
                    source={require('../assets/images/onboarding/english-letter.png')}
                    className="w-16 h-16"
                    resizeMode="contain"
                  />
                </View>
              </View>
            </View>
          </TouchableOpacity>

          {/* Arabic Card */}
          <TouchableOpacity
            className="flex-1 h-48 overflow-hidden rounded-2xl"
            onPress={() => handleLanguageSelect('arabic')}
            activeOpacity={0.8}
          >
            <View className="relative flex-1 bg-teal-400">
              {/* Background Image */}
              <ImageBackground
                source={require('../assets/images/onboarding/arabic.png')}
                className="absolute bottom-0 left-0 right-0 h-20"
                resizeMode="cover"
              />

              {/* Content */}
              <View className="justify-between flex-1 p-4">
                <View className="flex-row justify-between">
                  {/* Language Text */}
                  <Text className="text-lg text-white font-cairo-semibold">
                    العربية
                  </Text>

                  {/* Radio Button */}
                  <View className="items-end">
                    <View className={`w-6 h-6 rounded-full border-2 border-white ${selectedLanguage === 'arabic' ? 'bg-white' : 'bg-transparent'
                      }`}>
                      {selectedLanguage === 'arabic' && (
                        <View className="w-2.5 h-2.5 bg-teal-400 rounded-full m-auto" />
                      )}
                    </View>
                  </View>
                </View>
                
                {/* Letter Image */}
                <View className="items-center mb-8">
                  <Image
                    source={require('../assets/images/onboarding/arabic-letter.png')}
                    className="w-16 h-16"
                    resizeMode="contain"
                  />
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Continue Button */}
      <View className="px-6 pb-8">
        <TouchableOpacity
          className="py-4 bg-primary rounded-xl"
          onPress={handleContinue}
          activeOpacity={0.8}
        >
          <Text className="text-lg text-center text-black font-cairo-medium">
            Continue
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
